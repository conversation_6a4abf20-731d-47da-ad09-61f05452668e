import Header from '@/components/navigation/Header'

export default function ServicesPage() {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      <main className="pt-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center mb-12">
            <h1 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6">
              Our <span className="text-blue-600">Services</span>
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Comprehensive real estate services to meet all your property needs
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                title: 'Property Search',
                description: 'Find your perfect property with our advanced search tools and expert guidance.',
                icon: '🔍'
              },
              {
                title: 'Property Listing',
                description: 'List your property with professional photography and marketing support.',
                icon: '🏠'
              },
              {
                title: 'Investment Consultation',
                description: 'Get expert advice on real estate investment opportunities.',
                icon: '💼'
              },
              {
                title: 'Property Management',
                description: 'Full-service property management for landlords and investors.',
                icon: '🔧'
              },
              {
                title: 'Market Analysis',
                description: 'Detailed market reports and property valuations.',
                icon: '📊'
              },
              {
                title: 'Legal Support',
                description: 'Professional legal assistance for all property transactions.',
                icon: '⚖️'
              }
            ].map((service, index) => (
              <div key={index} className="bg-white rounded-lg shadow-lg p-8 hover:shadow-xl transition-shadow">
                <div className="text-4xl mb-4">{service.icon}</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">{service.title}</h3>
                <p className="text-gray-600 mb-6">{service.description}</p>
                <button className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                  Learn More
                </button>
              </div>
            ))}
          </div>
        </div>
      </main>
    </div>
  )
}
